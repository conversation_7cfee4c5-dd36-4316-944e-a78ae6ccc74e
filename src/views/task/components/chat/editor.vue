<template>
  <div class="max-h222px scroller">
    <div
      class="content"
      :class="{ 'has-content': hasContent }"
      :spellcheck="false"
      autocorrect="off"
      autocapitalize="off"
      translate="no"
      ref="contentRef"
      :data-placeholder="placeholder"
      @keydown="handleKeydown"
    >
      <!-- contenteditable="false"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @click="handleClick" -->
      <template v-for="item in content" :key="item.id">
        <span v-if="item.type === 'text'" contenteditable="false" class="cursor-default">
          {{ item.value }}
        </span>
        <AgentInput
          v-else-if="item.type === 'InputSlot'"
          :key="item.id"
          v-model:value="item.value"
          :placeholder="item.props?.placeholder"
          :slot-data="item"
        />
        <!-- @input="handleAgentInputChange"
          @focus="handleAgentInputFocus"
          @blur="handleAgentInputBlur" -->
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AgentInput from './agentInput/index.vue'
import { randomUUID } from '@/utils/util'
import { isNull } from 'lodash-es'

export interface PromptItemType {
  type: string
  id: string
  value: string
  props?: { placeholder: string; options?: string[] }
}

interface Props {
  placeholder?: string
  agentPrompt?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '给我布置一个任务（可以让我寻找海外商机、解析海外国家政策、跟踪海外行业新闻...）',
  agentPrompt: ''
})

const contentRef = ref<HTMLDivElement>()
const hasContent = ref(false)
const content = ref<PromptItemType[]>([])
let editKey = ''

// 解析agentPrompt内容，生成组件
function parseAgentPrompt(text: string): PromptItemType[] {
  text = text.replace(/\n/g, '')
  const result: PromptItemType[] = []
  let lastIndex = 0

  // 匹配 {"type": ...} 这样的 JSON 结构
  const regex = /(\{"type"[^{}]*(?:\{[^{}]*\}[^{}]*)*\})/g

  let match
  while ((match = regex.exec(text)) !== null) {
    const [fullMatch] = match

    // 提取前面的文本
    const precedingText = text.slice(lastIndex, match.index)
    if (precedingText) {
      result.push({ type: 'text', value: precedingText, id: randomUUID() })
    }

    // 尝试解析 JSON
    try {
      const json = JSON.parse(fullMatch)

      // 如果有 options 字段，转成数组
      if (json.props?.options && typeof json.props.options === 'string') {
        json.props.options = json.props.options.split(',')
      }

      result.push({ ...json, value: json.value ? json.value : '', id: randomUUID() })
    } catch (e) {
      console.warn('JSON 解析失败:', fullMatch)
      result.push({ type: 'text', value: fullMatch, id: randomUUID() })
    }

    lastIndex = regex.lastIndex
  }

  // 添加最后剩余的文本
  const remainingText = text.slice(lastIndex)
  if (remainingText) {
    result.push({ type: 'text', value: remainingText, id: randomUUID() })
  }

  return result
}

// // 处理空内容，为了防止删除所有内容后会自动生成br，阻碍了placeholder的显示
// const forceClearIfEmpty = () => {
//   if (!contentRef.value) return

//   const html = contentRef.value.innerHTML
//   const isBrOnly = html === '<br>' || html === '<br/>'
//   const isEmpty = html.trim() === ''

//   if (isBrOnly || isEmpty) {
//     contentRef.value.innerHTML = ''
//   }

//   // 更新 hasContent 状态
//   updateHasContent()
// }

onMounted(() => {
  content.value = parseAgentPrompt(props.agentPrompt)
  // 初始化 hasContent 状态
  // updateHasContent()
})

function handleInput(e: Event) {
  console.log('e: ', e)
  console.log('editKey: ', editKey)
  // 判断光标是不是在agentInput内
  if (editKey) {
    // const editIndex = content.value.findIndex(item => item.id === editKey)
    // editIndex
    // 'data-key'="c68bbbbf281ff1c9d50ac2848931433d"
    // 查找具有对应data-key的元素
    const targetElement = contentRef.value?.querySelector(`[data-key="${editKey}"]`)
    console.log('targetElement: ', targetElement)
    // if (targetElement) {
    //   const inputContent = targetElement.querySelector('.input-content')
    //   if (inputContent) {
    //     content.value[editIndex].value = inputContent.textContent || ''
    //   }
    // }
    // console.log('contentRef.value?.innerHTML: ', contentRef.value)
  }

  // 更新 hasContent 状态
  // updateHasContent()
}

// // 检查并更新 hasContent 状态
// function updateHasContent() {
//   if (!contentRef.value) return

//   const text = contentRef.value.innerText || ''

//   // 检查是否有实际内容
//   const hasText = text.trim() !== ''
//   const hasComponents = content.value.some(item => item.type === 'InputSlot' && item.value && item.value.trim() !== '')
//   hasContent.value = hasText || hasComponents
//   // console.log('updateHasContent - hasContent:', hasContent.value, 'text:', text, 'hasComponents:', hasComponents)
// }

// // 处理 AgentInput 组件的输入变化
// function handleAgentInputChange(value: string, slotData: PromptItemType) {
//   console.log('AgentInput 内容变化:', value, slotData)
//   // 更新对应的 content 项
//   const index = content.value.findIndex(item => item.id === slotData.id)
//   if (index !== -1) {
//     content.value[index].value = value
//   }

//   // 更新 hasContent 状态
//   updateHasContent()
// }

// // 处理 AgentInput 组件的焦点事件
// function handleAgentInputFocus(slotData: PromptItemType) {
//   console.log('AgentInput 获得焦点:', slotData)
//   editKey = slotData.id
// }

// // 处理 AgentInput 组件的失焦事件
// function handleAgentInputBlur(slotData: PromptItemType) {
//   console.log('AgentInput 失去焦点:', slotData)
//   // 可以在这里处理失焦逻辑，比如验证输入等
// }

function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'Enter') {
    e.preventDefault()
    console.log('回车触发', contentRef.value?.innerText)
  }

  // 监听左右按键
  if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
    // 获取当前选区
    const selection = window.getSelection()
    const range = selection?.getRangeAt(0)
    if (!range) return

    // 获取光标所在的元素
    const focusNode = range.startContainer

    if (focusNode.nodeType === Node.ELEMENT_NODE) {
      console.log('range: ', range, focusNode)
      console.log('dom节点')
      //   if (range.startOffset === 0) {
      //   }
    }

    // 获取focusNode的类型
    // if (focusNode.nodeType === Node.TEXT_NODE) {
    //   console.log(
    //     '纯文本',
    //     focusNode,
    //     range.startOffset,
    //     focusNode.textContent?.length,
    //     focusNode.textContent?.[range.startOffset - 1]
    //   )
    //   // 获取focusNode左右两边的节点
    //   const prevNode = focusNode.previousElementSibling
    //   const nextNode = focusNode.nextElementSibling
    //   console.log('左边节点:', prevNode, '右边节点:', nextNode)

    //   if (range.startOffset === 0) {
    //     console.log('光标在最左边', prevNode.querySelector('.input-content'))
    //     setRange(prevNode.querySelector('.input-content'), 0)
    //   } else if (range.startOffset === focusNode.textContent?.length) {
    //     console.log('光标在最右边', nextNode.querySelector('.input-content'))
    //     setRange(nextNode.querySelector('.input-content'), 2)
    //     // setRange(,0)
    //   }
    //   //  focusNode.length, focusNode.textContent[focusNode.length]
    // } else
    // }
    // console.log('focusNode: ', focusNode, focusNode.length, focusNode[focusNode.length])
    // if (!focusNode) return

    // // 检查是否在 agentInput 内
    // const agentInputElement = focusNode.closest('.agentInput')
    // console.log('agentInputElement: ', agentInputElement)
    // if (agentInputElement) {
    //   const dataKey = agentInputElement.getAttribute('data-key')
    //   editKey = dataKey || ''
    //   console.log('光标在 agentInput 内，dataKey:', dataKey)
    // } else {
    //   editKey = ''
    //   console.log('光标不在 agentInput 内')
    // }
  }
}

// function handleFocus() {
//   // 不做任何操作，保持placeholder显示
// }

// function handleBlur() {
//   forceClearIfEmpty()
// }

// function handleClick(e: MouseEvent) {
//   const target = e.target as HTMLElement
//   console.log('target: ', target)
//   // 如果点击的是agentInput或者placeholder，就获取光标
//   if (!(target.className.includes('agentInput') || target.className.includes('input-content'))) {
//     editKey = ''
//     return
//   }
//   // 判断是不是agentInput节点
//   const isAgentInputNode = target.className.includes('agentInput')
//   // 获取agentInput节点
//   const agentInputNode = isAgentInputNode ? target : target.parentElement
//   if (isNull(agentInputNode)) {
//     return
//   }
//   const dataKey = agentInputNode.getAttribute('data-key')
//   editKey = dataKey || ''
//   // const editIndex = content.value.findIndex(item => item.id === dataKey)
//   // 判断node内有没有文本，没有文本就设置光标位置，有就不设置
//   if (agentInputNode.innerText.trim() !== '') {
//     return
//   }
//   // 获取光标显示位置的node
//   const rangeTarget = agentInputNode.querySelector('.input-content') //.querySelector('.slot-side-left')
//   console.log('rangeTarget: ', rangeTarget)
//   if (isNull(rangeTarget)) {
//     return
//   }

//   setRange(rangeTarget, 0)
// }

// function setRange(dom: Node, index = 0) {
//   // 创建一个新的范围
//   const range = document.createRange()
//   const selection = window.getSelection()
//   // 将光标设置在agentInput节点的开始位置
//   range.setStartBefore(dom)
//   range.collapse(true)

//   // 应用选择
//   selection?.removeAllRanges()
//   selection?.addRange(range)
// }
</script>

<style lang="less" scoped>
.scroller {
  display: flex !important;
  align-items: flex-start !important;
  font-family: monospace;
  line-height: 1.4;
  height: 100%;
  overflow-x: auto;
  position: relative;
  z-index: 0;
  overflow-anchor: none;
}

.content {
  @apply min-h62px  fs-16px;
  outline: none;
  width: 100%;
  white-space: normal;
  word-break: break-word;
  overflow-wrap: anywhere;
  flex-shrink: 1;
  tab-size: 2;
  line-height: 30px;

  .textWarper {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    flex-direction: row;
  }

  &[data-placeholder]:not(.has-content) {
    &:empty,
    &:has(br:only-child) {
      &:before {
        content: attr(data-placeholder);
        color: #999;
        pointer-events: none;
      }
    }
  }
}
</style>
