<!--
 * @Author: 黄宏智(<PERSON><PERSON><PERSON>)
 * @Date: 2025-07-15 14:30:59
 * @LastEditors: 黄宏智(<PERSON><PERSON><PERSON>)
 * @LastEditTime: 2025-07-16 16:18:58
 * @FilePath: /global-intelligence-web/src/views/task/components/chat/agentInput/index.vue
 * @Description: 
-->
<template>
  <div class="agentInput" :data-key="slotData.id" contenteditable="false">
    <!-- <img class="widgetBuffer" aria-hidden="true" /> -->
    <!-- <span class="slot-side-left" contenteditable="false"></span> -->
    <span
      :class="['input-content', !props.value ? 'show-placeholder' : '']"
      :data-placeholder="placeholder"
      ref="inputRef"
      contenteditable="true"
      @input="handleInput"
    ></span>

    <!-- @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur" -->
    <!-- <span class="slot-side-right" contenteditable="false"></span> -->
    <!-- <img class="widgetBuffer" aria-hidden="true" /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, watchEffect } from 'vue'
import type { PromptItemType } from '../editor.vue'
import { isEmpty } from 'lodash-es'

interface Props {
  value: string
  placeholder?: string
  slotData: PromptItemType
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:value': [value: string]
  input: [value: string, slotData: PromptItemType]
  focus: [slotData: PromptItemType]
  blur: [slotData: PromptItemType]
}>()

const inputRef = ref<HTMLElement>()
// const currentValue = ref('')

watchEffect(() => {
  if (!inputRef.value) {
    return
  }
  console.log('props.value: ', props.value)

  // 如果内容已经相同，不需要更新
  if (inputRef.value.innerText === props.value) {
    return
  }

  // 保存当前光标位置
  const selection = window.getSelection()
  let cursorPosition = 0

  if (selection && selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    // 如果光标在当前元素内，保存位置
    if (inputRef.value.contains(range.startContainer)) {
      cursorPosition = range.startOffset
      // 如果光标在文本节点中，需要计算相对于元素开始的位置
      if (range.startContainer.nodeType === Node.TEXT_NODE) {
        const walker = document.createTreeWalker(inputRef.value, NodeFilter.SHOW_TEXT)
        let textOffset = 0
        let node
        while ((node = walker.nextNode())) {
          if (node === range.startContainer) {
            cursorPosition = textOffset + range.startOffset
            break
          }
          textOffset += node.textContent?.length || 0
        }
      }
    }
  }

  // 更新内容
  inputRef.value.innerText = props.value

  // 恢复光标位置
  if (cursorPosition > 0 && props.value.length > 0) {
    const newSelection = window.getSelection()
    if (newSelection) {
      const range = document.createRange()
      const walker = document.createTreeWalker(inputRef.value, NodeFilter.SHOW_TEXT)

      let currentOffset = 0
      let targetNode: Node | null = inputRef.value.firstChild

      // 如果有文本节点，找到对应位置
      let node
      while ((node = walker.nextNode())) {
        const nodeLength = node.textContent?.length || 0
        if (currentOffset + nodeLength >= cursorPosition) {
          targetNode = node as Node
          const offsetInNode = cursorPosition - currentOffset
          range.setStart(targetNode, Math.min(offsetInNode, nodeLength))
          range.setEnd(targetNode, Math.min(offsetInNode, nodeLength))
          break
        }
        currentOffset += nodeLength
      }

      // 如果没有找到合适的文本节点，设置到末尾
      if (!targetNode || targetNode === inputRef.value) {
        if (inputRef.value.firstChild) {
          const firstChild = inputRef.value.firstChild
          range.setStart(firstChild, Math.min(cursorPosition, firstChild.textContent?.length || 0))
          range.setEnd(firstChild, Math.min(cursorPosition, firstChild.textContent?.length || 0))
        } else {
          range.setStart(inputRef.value, 0)
          range.setEnd(inputRef.value, 0)
        }
      }

      range.collapse(true)
      newSelection.removeAllRanges()
      newSelection.addRange(range)
    }
  }
})

function handleInput(e: Event) {
  const value = inputRef.value?.innerHTML
  emit('update:value', value === '<br>' ? '' : inputRef.value?.innerText || '')
}

// function handleInput(e: Event) {
//   const target = e.target as HTMLElement
//   let value = target.innerText || ''

//   // 处理空内容的情况，清除可能存在的 br 标签
//   if (value.trim() === '' || value === '\n') {
//     value = ''
//     target.innerHTML = ''
//   }

//   currentValue.value = value
//   console.log('handleInput - currentValue:', currentValue.value, 'hasContent:', hasContent.value)
//   emit('update:value', value)
//   emit('input', value, props.slotData)
// }

// function handleFocus() {
//   console.log(123123123)

//   emit('focus', props.slotData)
// }

// function handleBlur() {
//   // 失焦时也检查并清理空内容
//   if (inputRef.value) {
//     const value = inputRef.value.innerText || ''
//     if (value.trim() === '' || value === '\n') {
//       inputRef.value.innerHTML = ''
//       currentValue.value = ''
//       emit('update:value', '')
//       emit('input', '', props.slotData)
//     }
//   }
//   emit('blur', props.slotData)
// }

// // 监听 value 变化，同步到 DOM 和 currentValue
// watch(
//   () => props.value,
//   newValue => {
//     console.log('newValue: ', newValue);
//     const value = newValue || ''
//     currentValue.value = value
//     if (inputRef.value && inputRef.value.innerText !== value) {
//       inputRef.value.innerText = value
//     }
//   },
//   { immediate: true }
// )

// // 监听 currentValue 变化，同步到 DOM
// watch(
//   currentValue,
//   newValue => {
//     if (inputRef.value && inputRef.value.innerText !== newValue) {
//       inputRef.value.innerText = newValue
//     }
//   },
//   { immediate: true }
// )
</script>

<style scoped lang="less">
@slotRadius: 8px;
@slotBgColor: #cfc5f2;
@slotMargin: 4px;
@slotPaddingX: 6px;
@slotPaddingY: 2px;
@slotHeight: 26px;

.agentInput {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  height: @slotHeight;
  line-height: @slotHeight;
  margin: 0 @slotMargin;
  border-radius: @slotRadius;
  padding: @slotPaddingY @slotPaddingX;
  background: @slotBgColor;
  color: #6553ee;
  cursor: text;
  outline: none;
}

.input-content {
  outline: none;
  min-width: 1px;
  color: #6553ee;

  // 只在空内容且有 show-placeholder 类时显示 placeholder
  &.show-placeholder:empty::before {
    content: attr(data-placeholder);
    color: #f4f0ff;
    pointer-events: none;
  }

  // 处理可能存在的 br 标签情况
  // &.show-placeholder:has(br:only-child)::before {
  //   content: attr(data-placeholder);
  //   color: #f4f0ff;
  //   pointer-events: none;
  // }
}

.widgetBuffer,
.slot-side-right,
.slot-side-left {
  vertical-align: text-top;
  height: @slotHeight;
  width: 0;
  display: inline;
}

// .slot-side-left {
//   border-radius: @slotRadius 0 0 @slotRadius;
//   padding: @slotPaddingY 0 @slotPaddingY @slotPaddingX;
//   background: @slotBgColor;
//   margin-left: @slotMargin;
// }
// .slot-side-right {
//   border-radius: 0 @slotRadius @slotRadius 0;
//   padding: @slotPaddingY @slotPaddingX @slotPaddingY 0;
//   background: @slotBgColor;
//   margin-right: @slotMargin;
// }

// .inputSlot {
//   padding: @slotPaddingY 0;
// }
</style>
